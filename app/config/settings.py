import os
from typing import Dict, List, Optional
from pydantic import BaseSettings, Field, validator


class TelegramConfig(BaseSettings):
    """Telegram bot configuration."""
    bot_token: str = Field(..., env="TELEGRAM_BOT_TOKEN")
    webhook_url: Optional[str] = Field(None, env="TELEGRAM_WEBHOOK_URL")
    webhook_secret: Optional[str] = Field(None, env="TELEGRAM_WEBHOOK_SECRET")
    
    class Config:
        env_prefix = "TELEGRAM_"


class LLMConfig(BaseSettings):
    """LLM service configuration."""
    primary_provider: str = Field("gemini", env="LLM_PRIMARY_PROVIDER")
    fallback_providers: List[str] = Field(["deepseek"], env="LLM_FALLBACK_PROVIDERS")
    
    # Gemini configuration
    gemini_api_key: Optional[str] = Field(None, env="GEMINI_API_KEY")
    gemini_model: str = Field("gemini-pro", env="GEMINI_MODEL")
    
    # Deepseek configuration
    deepseek_api_key: Optional[str] = Field(None, env="DEEPSEEK_API_KEY")
    deepseek_model: str = Field("deepseek-chat", env="DEEPSEEK_MODEL")
    deepseek_base_url: str = Field("https://api.deepseek.com", env="DEEPSEEK_BASE_URL")
    
    # OpenAI configuration (for testing)
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    openai_model: str = Field("gpt-3.5-turbo", env="OPENAI_MODEL")
    
    max_tokens: int = Field(4000, env="LLM_MAX_TOKENS")
    temperature: float = Field(0.7, env="LLM_TEMPERATURE")
    timeout: int = Field(30, env="LLM_TIMEOUT")
    
    class Config:
        env_prefix = "LLM_"


class DatabaseConfig(BaseSettings):
    """Database configuration."""
    mongodb_url: str = Field("mongodb://localhost:27017", env="MONGODB_URL")
    database_name: str = Field("zeitwahl", env="DATABASE_NAME")
    
    # Redis configuration for caching
    redis_url: str = Field("redis://localhost:6379", env="REDIS_URL")
    redis_db: int = Field(0, env="REDIS_DB")
    cache_ttl: int = Field(3600, env="CACHE_TTL")  # 1 hour
    
    class Config:
        env_prefix = "DB_"


class CalendarConfig(BaseSettings):
    """Calendar service configuration."""
    google_credentials_file: Optional[str] = Field(None, env="GOOGLE_CREDENTIALS_FILE")
    google_client_id: Optional[str] = Field(None, env="GOOGLE_CLIENT_ID")
    google_client_secret: Optional[str] = Field(None, env="GOOGLE_CLIENT_SECRET")
    
    outlook_client_id: Optional[str] = Field(None, env="OUTLOOK_CLIENT_ID")
    outlook_client_secret: Optional[str] = Field(None, env="OUTLOOK_CLIENT_SECRET")
    outlook_tenant_id: Optional[str] = Field(None, env="OUTLOOK_TENANT_ID")
    
    default_timezone: str = Field("UTC", env="DEFAULT_TIMEZONE")
    
    class Config:
        env_prefix = "CALENDAR_"


class AppConfig(BaseSettings):
    """Main application configuration."""
    environment: str = Field("development", env="ENVIRONMENT")
    debug: bool = Field(False, env="DEBUG")
    log_level: str = Field("INFO", env="LOG_LEVEL")
    
    # Rate limiting
    rate_limit_requests: int = Field(10, env="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(60, env="RATE_LIMIT_WINDOW")  # seconds
    
    # Security
    secret_key: str = Field("dev-secret-key", env="SECRET_KEY")
    
    @validator("environment")
    def validate_environment(cls, v):
        if v not in ["development", "staging", "production"]:
            raise ValueError("Environment must be development, staging, or production")
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v):
        if v not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            raise ValueError("Invalid log level")
        return v
    
    class Config:
        env_prefix = "APP_"


class Settings:
    """Centralized settings management."""
    
    def __init__(self):
        self.app = AppConfig()
        self.telegram = TelegramConfig()
        self.llm = LLMConfig()
        self.database = DatabaseConfig()
        self.calendar = CalendarConfig()
    
    @property
    def is_development(self) -> bool:
        return self.app.environment == "development"
    
    @property
    def is_production(self) -> bool:
        return self.app.environment == "production"


# Global settings instance
settings = Settings()
