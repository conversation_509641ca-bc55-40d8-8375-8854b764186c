#!/usr/bin/env python3
"""
Zeitwahl AI Agent - Example Usage (Preprocessing Only)

This script demonstrates the message flow from Telegram bot through preprocessing only.
It excludes LLM processing and postprocessing to focus on the initial pipeline stages.
"""

import asyncio
import logging
import os
from datetime import datetime

from app.utils.event_bus import event_bus
from app.utils.events import MessageReceived, MessagePreprocessed
from app.preprocess import Preprocessor


class ExampleUsage:
    """Example usage scenarios focusing on bot chat and preprocessing only."""

    def __init__(self):
        self.setup_logging()
        self.preprocessor = None
        self.preprocessed_messages = []

    def setup_logging(self):
        """Setup logging for the example."""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        # Reduce noise from libraries
        logging.getLogger('aiogram').setLevel(logging.WARNING)

    async def initialize_components(self):
        """Initialize preprocessing components only."""
        print("🔧 Initializing preprocessing components...")

        # Initialize only the preprocessor
        self.preprocessor = Preprocessor()

        # Subscribe to MessagePreprocessed events to capture results
        await event_bus.subscribe_tagged_methods(self)

        print("✅ Preprocessing components initialized successfully!")

    @event_bus.subscribe(MessagePreprocessed)
    async def capture_preprocessed_message(self, event: MessagePreprocessed):
        """Capture preprocessed messages for demonstration purposes."""
        self.preprocessed_messages.append({
            "user_id": event.user_id,
            "original_message": event.original_message,
            "processed_prompt": event.processed_prompt[:200] + "..." if len(event.processed_prompt) > 200 else event.processed_prompt,
            "available_tools": event.available_tools,
            "user_context_keys": list(event.user_context.keys()) if event.user_context else []
        })
        print(f"📋 Preprocessed message from user {event.user_id}")
        print(f"   Original: {event.original_message}")
        print(f"   Available tools: {', '.join(event.available_tools) if event.available_tools else 'None'}")
        print(f"   Context keys: {', '.join(list(event.user_context.keys())) if event.user_context else 'None'}")
        print(f"   Prompt length: {len(event.processed_prompt)} characters")

    async def simulate_user_message(self, user_id: int, chat_id: int, message_text: str):
        """Simulate a user sending a message."""
        print(f"📥 User {user_id}: {message_text}")

        # Create MessageReceived event
        message_event = MessageReceived(
            user_id=user_id,
            chat_id=chat_id,
            message_id=len(self.preprocessed_messages) + 1,
            message_text=message_text,
            username="demo_user",
            first_name="Demo",
            last_name="User"
        )

        # Publish the event to trigger preprocessing
        await event_bus.publish(message_event)

        # Wait for preprocessing to complete
        await asyncio.sleep(1.0)

    async def demo_basic_preprocessing(self):
        """Demonstrate basic message preprocessing flow."""
        print("\n🎯 Demo: Message Preprocessing Flow")
        print("=" * 50)

        user_id = 12345
        chat_id = 67890

        # Simulate various user messages to test preprocessing
        messages = [
            "Hello! Can you help me with my calendar?",
            "I need to schedule a meeting tomorrow at 2 PM",
            "What's on my calendar today?",
            "Find me a free hour this week for a team meeting",
            "Cancel my 3 PM meeting today",
            "Schedule a recurring weekly standup every Monday at 9 AM",
            "Move my 2 PM meeting to 3 PM to avoid conflicts"
        ]

        for i, message in enumerate(messages, 1):
            print(f"\n--- Message {i}/{len(messages)} ---")
            await self.simulate_user_message(user_id, chat_id, message)
            print()  # Add spacing between messages

    async def demo_message_validation(self):
        """Demonstrate message validation during preprocessing."""
        print("\n🔍 Demo: Message Validation")
        print("=" * 50)

        user_id = 99999
        chat_id = 88888

        # Test various validation scenarios
        validation_scenarios = [
            ("Valid message", "Schedule a meeting tomorrow at 2 PM"),
            ("Empty message", ""),
            ("Very long message", "x" * 5000),
            ("Potentially malicious", "<script>alert('test')</script>Hello"),
            ("Spam-like content", "BUY BUY BUY NOW NOW NOW CHEAP CHEAP CHEAP!!!"),
            ("Special characters", "Meeting with @john & @jane #important $budget"),
        ]

        for i, (scenario_name, message) in enumerate(validation_scenarios, 1):
            print(f"\n--- Validation Test {i}: {scenario_name} ---")
            if len(message) > 100:
                print(f"📥 Testing: {message[:100]}... (truncated)")
            else:
                print(f"📥 Testing: {message}")

            await self.simulate_user_message(user_id, chat_id, message)

    async def demo_rate_limiting(self):
        """Demonstrate rate limiting during preprocessing."""
        print("\n⏱️ Demo: Rate Limiting")
        print("=" * 50)

        user_id = 77777
        chat_id = 66666

        print("Sending multiple messages rapidly to test rate limiting...")

        # Send messages rapidly to trigger rate limiting
        for i in range(12):  # Send more than the rate limit (10)
            message = f"Rapid message {i+1}"
            print(f"📥 Sending: {message}")
            await self.simulate_user_message(user_id, chat_id, message)
            await asyncio.sleep(0.1)  # Small delay between messages

        print("\n✅ Rate limiting test completed")

    async def demo_context_building(self):
        """Demonstrate context building with different user scenarios."""
        print("\n🧠 Demo: Context Building")
        print("=" * 50)

        # Test with different users to see context differences
        users = [
            (11111, "New user with no history"),
            (22222, "User with some conversation history"),
            (33333, "Power user with lots of activity")
        ]

        for user_id, description in users:
            print(f"\n--- Testing: {description} ---")
            chat_id = user_id + 1000

            # Send a few messages to build context
            messages = [
                "Hello, I'm new here",
                "Can you help me schedule a meeting?",
                "What integrations do you support?"
            ]

            for message in messages:
                await self.simulate_user_message(user_id, chat_id, message)
                await asyncio.sleep(0.2)  # Small delay between messages

    async def run_all_demos(self):
        """Run all preprocessing demonstration scenarios."""
        print("🚀 Starting Zeitwahl AI Agent Preprocessing Demo")
        print("=" * 60)

        await self.initialize_components()

        # Run preprocessing-focused demos
        await self.demo_basic_preprocessing()
        await self.demo_message_validation()
        await self.demo_rate_limiting()
        await self.demo_context_building()

        print("\n🎉 Preprocessing demonstration completed!")
        print(f"📊 Total messages preprocessed: {len(self.preprocessed_messages)}")

        # Show summary of preprocessing results
        if self.preprocessed_messages:
            print("\n📋 Preprocessing Summary:")
            for i, msg in enumerate(self.preprocessed_messages[-5:], 1):  # Show last 5
                print(f"   {i}. User {msg['user_id']}: {msg['original_message'][:50]}...")
                print(f"      Tools: {', '.join(msg['available_tools']) if msg['available_tools'] else 'None'}")

        # Clean up
        await event_bus.clear_subscribers()


async def main():
    """Main function to run the preprocessing demo."""
    # Set environment variables for demo (if not already set)
    if not os.getenv("TELEGRAM_BOT_TOKEN"):
        os.environ["TELEGRAM_BOT_TOKEN"] = "demo_token_not_for_real_use"

    # Create and run the example
    example = ExampleUsage()
    await example.run_all_demos()


if __name__ == "__main__":
    print("Zeitwahl AI Agent - Preprocessing Demo")
    print("This script demonstrates message preprocessing without requiring LLM APIs.")
    print("It shows how messages flow from bot reception through validation, ")
    print("user identification, context building, and prompt construction.")
    print()

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
